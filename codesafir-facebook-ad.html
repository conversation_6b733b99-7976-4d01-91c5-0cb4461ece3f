<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeSafir Facebook Ad</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .facebook-ad {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #1A1F71 0%, #0F1357 100%);
            position: relative;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 50px;
            color: white;
        }

        .background-device {
            position: absolute;
            right: -150px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.08;
            z-index: 1;
        }

        .laptop-mockup {
            width: 400px;
            height: 250px;
            background: #FFFFFF;
            border-radius: 15px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .laptop-mockup::before {
            content: "";
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 420px;
            height: 25px;
            background: #E0E0E0;
            border-radius: 0 0 25px 25px;
        }

        .laptop-screen {
            width: 360px;
            height: 200px;
            background: linear-gradient(45deg, #00BFA6, #1A1F71);
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
        }

        .content-wrapper {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 56px;
            font-weight: 900;
            color: #FFFFFF;
            letter-spacing: 3px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .logo .code {
            color: #00BFA6;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .headline {
            font-size: 48px;
            font-weight: 700;
            color: #FFFFFF;
            margin-bottom: 50px;
            line-height: 1.2;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .features-grid {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            max-width: 200px;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .icon-tips {
            background: linear-gradient(45deg, #00BFA6, #00D4AA);
            color: white;
        }

        .icon-discounts {
            background: linear-gradient(45deg, #FFD600, #FFC107);
            color: #1A1F71;
        }

        .icon-qa {
            background: linear-gradient(45deg, #00BFA6, #1A1F71);
            color: white;
        }

        .feature-text {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
            line-height: 1.4;
        }

        .cta-section {
            text-align: center;
        }

        .cta-button {
            background: linear-gradient(45deg, #FFD600, #FFC107);
            color: #1A1F71;
            font-size: 32px;
            font-weight: 700;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(255, 214, 0, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 214, 0, 0.5);
        }

        .accent-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
        }

        .shape1 {
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(0, 191, 166, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            top: -150px;
            left: -150px;
        }

        .shape2 {
            position: absolute;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 214, 0, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            bottom: -100px;
            right: 100px;
        }

        .shape3 {
            position: absolute;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(0, 191, 166, 0.08) 0%, transparent 70%);
            border-radius: 50%;
            top: 100px;
            right: -75px;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 1200px) {
            .facebook-ad {
                width: 100vw;
                height: 52.5vw;
                min-height: 400px;
                padding: 30px;
            }
            
            .headline {
                font-size: 36px;
            }
            
            .features-grid {
                gap: 40px;
            }
            
            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }
            
            .cta-button {
                font-size: 24px;
                padding: 15px 35px;
            }
        }
    </style>
</head>
<body>
    <div class="facebook-ad">
        <div class="accent-shapes">
            <div class="shape1"></div>
            <div class="shape2"></div>
            <div class="shape3"></div>
        </div>

        <div class="background-device">
            <div class="laptop-mockup">
                <div class="laptop-screen"></div>
            </div>
        </div>

        <div class="content-wrapper">
            <div class="logo-section">
                <div class="logo">
                    <span class="code">Code</span>Safir
                </div>
            </div>

            <div class="main-content">
                <h1 class="headline">
                    Follow CodeSafir for<br>
                    Web Tips & Deals
                </h1>

                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon icon-tips">💡</div>
                        <div class="feature-text">Weekly web dev tips</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon icon-discounts">💰</div>
                        <div class="feature-text">Exclusive discounts on websites</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon icon-qa">🎯</div>
                        <div class="feature-text">Live Q&A sessions</div>
                    </div>
                </div>
            </div>

            <div class="cta-section">
                <button class="cta-button">
                    Like Our Page
                </button>
            </div>
        </div>
    </div>
</body>
</html>
